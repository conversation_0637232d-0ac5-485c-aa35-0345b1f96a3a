# Data Science Platform

A comprehensive platform for data analysis, visualization, machine learning with AI-powered assistance and an Excel-inspired user interface.

## Features

- **Data Upload**: Upload CSV or Excel files for analysis
- **Data Profiling**: Explore and understand your dataset with detailed statistics
- **Data Cleaning**: Clean and prepare your data with advanced tools
- **Machine Learning**: Train and evaluate machine learning models
- **Data Visualization**: Create interactive charts and visualizations
- **AI Agent**: Intelligent assistance for data analysis and code generation
- **Excel-Inspired UI**: Familiar interface with Excel-like styling and interactions
- **Specialized Agents**: Task-specific AI agents for enhanced functionality

## Tech Stack

- **Backend**: FastAPI, Python, scikit-learn, pandas, numpy
- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **AI**: Google Gemini AI integration
- **UI Design**: Excel-inspired design system with custom CSS variables

## Project Structure

```
whatsapp-bridge/
├── backend/               # FastAPI backend
│   ├── app/              # Application code
│   │   ├── routers/      # API routes
│   │   │   ├── ai_agent.py
│   │   │   ├── data_cleaning.py
│   │   │   ├── data_editor.py
│   │   │   ├── data_profile.py
│   │   │   ├── data_upload.py
│   │   │   ├── data_visualization.py
│   │   │   ├── machine_learning.py
│   │   │   └── specialized_agents.py
│   │   └── utils/        # Utility functions
│   │       ├── code_executor.py
│   │       ├── file_handler.py
│   │       ├── gemini_client.py
│   │       ├── session_manager.py
│   │       └── specialized_agents.py
│   ├── main.py          # Main application entry point
│   └── requirements.txt  # Python dependencies
│
├── frontend/            # Next.js frontend
│   ├── public/         # Static assets
│   └── src/           # Source code
│       ├── app/       # Next.js app router
│       │   ├── agent/
│       │   ├── cleaning/
│       │   ├── ml/
│       │   ├── playground/
│       │   ├── profile/
│       │   └── visualization/
│       ├── components/ # React components
│       │   ├── agent/
│       │   ├── data/
│       │   ├── layout/
│       │   └── ui/
│       └── lib/       # Utility functions
```

## Getting Started

### Prerequisites

- Python 3.8+
- Node.js 18+
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

5. Start the backend server:
   ```
   uvicorn main:app --reload
   ```

The API will be available at http://localhost:8000.

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

The frontend will be available at http://localhost:3000.

## Usage

1. Open the application in your browser at http://localhost:3000
2. Upload a CSV or Excel file on the home page
3. Navigate through the different sections to analyze and visualize your data
4. Use the AI agent for assistance with data analysis and code generation
5. Clean your data and train machine learning models

### Key Features

- **AI Agent Interface**
  - Interactive chat interface for data analysis assistance
  - Code generation and execution
  - Specialized agents for specific tasks
  
- **Data Management**
  - Excel-like data editor
  - Advanced data cleaning tools
  - Comprehensive data profiling

- **Interactive Features**
  - Tooltips for additional information
  - Hover effects on tables and interactive elements
  - Excel-like data presentation and formatting
  - Real-time data visualization

> Note: Screenshots are placeholders. Replace with actual application screenshots.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
