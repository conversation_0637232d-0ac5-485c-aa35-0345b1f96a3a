from typing_extensions import TypeAliasType
from typing import Literal


KnownModelName = TypeAliasType(
    "KnownModelName",
    Literal[
        # Anthropic Models
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-3-7-sonnet-20250219",
        "anthropic/claude-3-5-sonnet-latest",
        "anthropic/claude-3-5-sonnet-20241022",
        "anthropic/claude-3-5-sonnet-20240620",
        "anthropic/claude-3-5-haiku-latest",
        "anthropic/claude-3-5-haiku-20241022",
        "anthropic/claude-3-opus-latest",
        "anthropic/claude-3-opus-20240229",
        "anthropic/claude-3-haiku-20240307",
        # Cohere Models
        "cohere/c4ai-aya-expanse-32b",
        "cohere/c4ai-aya-expanse-8b",
        "cohere/command",
        "cohere/command-light",
        "cohere/command-light-nightly",
        "cohere/command-nightly",
        "cohere/command-r",
        "cohere/command-r-03-2024",
        "cohere/command-r-08-2024",
        "cohere/command-r-plus",
        "cohere/command-r-plus-04-2024",
        "cohere/command-r-plus-08-2024",
        "cohere/command-r7b-12-2024",
        # OpenAI Models
        "openai/gpt-3.5-turbo",
        "openai/gpt-3.5-turbo-0125",
        "openai/gpt-3.5-turbo-1106",
        "openai/gpt-3.5-turbo-16k",
        "openai/gpt-4",
        "openai/gpt-4-0125-preview",
        "openai/gpt-4-0613",
        "openai/gpt-4-1106-preview",
        "openai/gpt-4-32k",
        "openai/gpt-4-32k-0613",
        "openai/gpt-4-turbo",
        "openai/gpt-4-turbo-2024-04-09",
        "openai/gpt-4-turbo-preview",
        "openai/gpt-4.1",
        "openai/gpt-4.1-2025-04-14",
        "openai/gpt-4.1-mini",
        "openai/gpt-4.1-mini-2025-04-14",
        "openai/gpt-4.1-nano",
        "openai/gpt-4.1-nano-2025-04-14",
        "openai/gpt-4o",
        "openai/gpt-4o-2024-05-13",
        "openai/gpt-4o-2024-08-06",
        "openai/gpt-4o-2024-11-20",
        "openai/gpt-4o-audio-preview",
        "openai/gpt-4o-audio-preview-2024-10-01",
        "openai/gpt-4o-audio-preview-2024-12-17",
        "openai/gpt-4o-mini",
        "openai/gpt-4o-mini-2024-07-18",
        # Groq Models
        "groq/gemma2-9b-it",
        "groq/llama-3.3-70b-versatile",
        "groq/llama-3.1-8b-instant",
        "groq/llama3-70b-8192",
        "groq/llama3-8b-8192",
        "groq/qwen-qwq-32b",
        # Mistral
        "mistral/codestral-latest",
        "mistral/mistral-large-latest",
        "mistral/mistral-small-latest",
        "mistral/pixtral-large-latest",
        "mistral/mistral-saba-latest",
        "mistral/ministral-3b-latest",
        "mistral/ministral-8b-latest",
        # Google Models
        "google/gemini-1.5-flash",
        "google/gemini-1.5-flash-8b",
        "google/gemini-1.5-pro",
        "google/gemini-2.0-flash-exp",
        "google/gemini-2.0-flash-thinking-exp-01-21",
        "google/gemini-exp-1206",
        "google/gemini-2.0-flash",
        "google/gemini-2.0-flash-lite-preview-02-05",
        "google/gemini-2.0-pro-exp-02-05",
        "google/gemini-2.5-flash-preview-04-17",
        "google/gemini-2.5-pro-exp-03-25",
        "google/gemini-2.5-pro-preview-03-25",
        # VertexAI Models
        "vertexai/gemini-1.5-flash",
        "vertexai/gemini-1.5-pro",
        "vertexai/gemini-2.0-flash-exp",
        "vertexai/gemini-2.0-flash-001",
        "vertexai/gemini-2.0-flash-lite",
        "vertexai/gemini-2.5-pro-preview-03-25",
        "vertexai/gemini-2.5-pro-exp-03-25",
        "vertexai/gemini-2.5-flash-preview-04-17",
        # Generative AI models
        "generative-ai/gemini-1.5-flash",
        "generative-ai/gemini-1.5-flash-8b",
        "generative-ai/gemini-1.5-pro",
        "generative-ai/gemini-2.0-flash-exp",
        "generative-ai/gemini-2.0-flash-thinking-exp-01-21",
        "generative-ai/gemini-exp-1206",
        "generative-ai/gemini-2.0-flash",
        "generative-ai/gemini-2.0-flash-lite-preview-02-05",
        "generative-ai/gemini-2.0-pro-exp-02-05",
        "generative-ai/gemini-2.5-flash-preview-04-17",
        "generative-ai/gemini-2.5-pro-exp-03-25",
        "generative-ai/gemini-2.5-pro-preview-03-25",
        # Fireworks AI
        "fireworks/accounts/fireworks/models/llama4-maverick-instruct-basic",
        "fireworks/accounts/fireworks/models/llama-v3p1-405b-instruct",
        "fireworks/accounts/fireworks/models/llama4-scout-instruct-basic",
        "fireworks/accounts/fireworks/models/qwen3-30b-a3b",
        "fireworks/accounts/fireworks/models/qwen3-235b-a22b",
        "fireworks/accounts/fireworks/models/deepseek-v3",
        "fireworks/accounts/fireworks/models/llama-v3p1-8b-instruct",
        "fireworks/accounts/fireworks/models/llama-v3p3-70b-instruct",
        # Cerebras
        "cerebras/llama-4-scout-17b-16e-instruct",
        "cerebras/llama3.1-8b",
        "cerebras/llama-3.3-70b",
        # Writer
        "writer/palmyra-x5",
        "writer/palmyra-x4",
        # Perplexity
        "perplexity/sonar-deep-research",
        "perplexity/sonar-reasoning-pro",
        "perplexity/sonar-pro",
        "perplexity/sonar",
        "perplexity/r1-1776",
    ],
)
