{"$schema": "http://json-schema.org/draft-07/schema#", "title": "HuggingFace Embedding Function Schema", "description": "Schema for the HuggingFace embedding function configuration", "version": "1.0.0", "type": "object", "properties": {"model_name": {"type": "string", "description": "The name of the model to use for text embeddings"}, "api_key_env_var": {"type": "string", "description": "Environment variable name that contains your API key for the HuggingFace API"}}, "required": ["api_key_env_var", "model_name"], "additionalProperties": false}